<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WearWise - Weather-Based Outfit Recommendations</title>
  
  <!-- Use Tailwind CSS directly -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- React and ReactDOM -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  
  <!-- Babel for JSX transformation -->
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  
  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- Weather Icons (more colorful weather icons) -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/weather-icons/2.0.10/css/weather-icons.min.css">
  
  <!-- Google Font -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Custom Styles -->

  <style>
    :root {
      --primary-gradient: linear-gradient(135deg, #3b82f6, #8b5cf6);
      --glass-bg: rgba(255, 255, 255, 0.6);
      --glass-border: rgba(255, 255, 255, 0.2);
      --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
    }
    
    body {
      font-family: 'Poppins', sans-serif;
      margin: 0;
      overflow-x: hidden;
      background-color: #f8fafc;
    }
    
    /* Refined glassmorphism effect */
    .glass {
      background: var(--glass-bg);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
      border: 1px solid var(--glass-border);
      box-shadow: var(--glass-shadow);
    }
    
    /* Animated fluid background */
    .fluid-background {
      position: fixed;
      width: 100vw;
      height: 100vh;
      top: 0;
      left: 0;
      z-index: -1;
      overflow: hidden;
    }
    
    .fluid-background::before,
    .fluid-background::after {
      content: "";
      position: absolute;
      width: 200%;
      height: 200%;
      top: -50%;
      left: -50%;
      z-index: -1;
    }
    
    .fluid-background::before {
      background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0) 60%);
      animation: fluid-motion 30s ease-in-out infinite alternate;
    }
    
    .fluid-background::after {
      background: radial-gradient(circle, rgba(139, 92, 246, 0.2) 0%, rgba(139, 92, 246, 0) 60%);
      animation: fluid-motion 25s ease-in-out 2s infinite alternate-reverse;
    }
    
    @keyframes fluid-motion {
      0% {
        transform: translate(-5%, -5%) rotate(0deg);
      }
      25% {
        transform: translate(5%, 5%) rotate(1deg);
      }
      50% {
        transform: translate(10%, -5%) rotate(2deg);
      }
      75% {
        transform: translate(-10%, 5%) rotate(1deg);
      }
      100% {
        transform: translate(-5%, 10%) rotate(0deg);
      }
    }
    
    /* Blob animations for background */
    .blob {
      position: absolute;
      border-radius: 50%;
      filter: blur(40px);
      opacity: 0.5;
      pointer-events: none;
      animation: blob-float 15s ease-in-out infinite alternate;
    }
    
    .blob:nth-child(1) {
      width: 400px;
      height: 400px;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.3), rgba(59, 130, 246, 0.05));
      top: -200px;
      right: -100px;
    }
    
    .blob:nth-child(2) {
      width: 350px;
      height: 350px;
      background: radial-gradient(circle, rgba(139, 92, 246, 0.3), rgba(139, 92, 246, 0.05));
      bottom: -150px;
      left: -100px;
      animation-delay: 3s;
    }
    
    .blob:nth-child(3) {
      width: 250px;
      height: 250px;
      background: radial-gradient(circle, rgba(252, 165, 165, 0.2), rgba(252, 165, 165, 0.05));
      top: 30%;
      left: 15%;
      animation-delay: 5s;
    }
    
    @keyframes blob-float {
      0% {
        transform: translate(0, 0) scale(1);
      }
      33% {
        transform: translate(30px, -50px) scale(1.05);
      }
      66% {
        transform: translate(-20px, 20px) scale(0.95);
      }
      100% {
        transform: translate(0, 0) scale(1);
      }
    }
    
    /* Weather icons styles */
    .weather-icon {
      font-size: 2rem;
      filter: drop-shadow(0 4px 3px rgba(0, 0, 0, 0.07));
    }
    
    .weather-icon.large {
      font-size: 4rem;
    }
    
    .wi-day-sunny {
      color: #f97316; /* orange-500 */
    }
    
    .wi-day-cloudy, .wi-cloudy {
      color: #64748b; /* slate-500 */
    }
    
    .wi-day-sunny-overcast, .wi-day-cloudy-high {
      color: #94a3b8; /* slate-400 */
    }
    
    .wi-rain, .wi-day-rain {
      color: #3b82f6; /* blue-500 */
    }
    
    .wi-fog, .wi-day-fog {
      color: #cbd5e1; /* slate-300 */
    }
    
    /* Day selector styles */
    .day-selector {
      display: flex;
      justify-content: space-between;
      gap: 8px;
      padding: 12px;
      margin-bottom: 16px;
      border-radius: 16px;
    }
    
    .day-pill {
      flex: 1;
      padding: 12px 8px;
      border-radius: 12px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .day-pill.active {
      background: var(--primary-gradient);
      color: white;
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
    }
    
    .day-pill:not(.active) {
      background: rgba(255, 255, 255, 0.7);
      color: #64748b;
    }
    
    .day-pill:not(.active):hover {
      background: rgba(255, 255, 255, 0.9);
      transform: translateY(-2px);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
    }
    
    /* Animations */
    @keyframes fade-in {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes slide-up {
      from { opacity: 0; transform: translateY(30px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
    
    .animate-fade-in {
      animation: fade-in 0.6s ease-out forwards;
    }
    
    .animate-slide-up {
      animation: slide-up 0.5s ease-out forwards;
    }
    
    .animate-pulse {
      animation: pulse 2s infinite;
    }
    
    /* Card styles with consistent spacing */
    .weather-card {
      border-radius: 20px;
      overflow: hidden;
      margin-bottom: 24px;
    }
    
    .weather-card-header {
      background: var(--primary-gradient);
      padding: 16px 24px;
      color: white;
    }
    
    .weather-card-body {
      padding: 24px;
    }
    
    .detail-container {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      margin-top: 16px;
    }
    
    .detail-box {
      padding: 16px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.7);
      transition: all 0.3s ease;
    }
    
    .detail-box:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }
    
    .outfit-section {
      padding: 20px;
      border-radius: 16px;
    }
    
    .outfit-item {
      padding: 12px 16px;
      margin-bottom: 12px;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    .outfit-item:hover {
      background: rgba(59, 130, 246, 0.1);
    }
    
    /* Accessibility improvements */
    .text-contrast {
      color: #1e293b; /* slate-800 for better readability */
    }
    
    .text-label {
      color: #64748b; /* slate-500 for labels */
    }
    
    .text-value {
      color: #0f172a; /* slate-900 for values */
      font-weight: 600;
    }
    
    /* Consistent spacing */
    .section-spacing {
      margin-bottom: 24px;
    }
    
    .content-padding {
      padding: 20px 24px;
    }
    
    /* Better focus states for accessibility */
    button:focus, 
    a:focus {
      outline: 2px solid #3b82f6;
      outline-offset: 2px;
    }
  </style>
</head>
<body>
  <!-- Animated fluid background -->
  <div class="fluid-background">
    <div class="blob"></div>
    <div class="blob"></div>
    <div class="blob"></div>
  </div>
  
  <div id="root"></div>

  <!-- Include all scripts with correct type -->
  <script type="text/babel" src="../src/utils/outfitRecommender.js"></script>
  <script type="text/babel" src="../src/hooks/WeatherData.js"></script>
  <script type="text/babel" src="../src/components/WeatherIcon.jsx"></script>
  <script type="text/babel" src="../src/components/WeatherDetail.jsx"></script>
  <script type="text/babel" src="../src/components/SuitabilityBar.jsx"></script>
  <script type="text/babel" src="../src/components/OutfitRecommendation.jsx"></script>
  <script type="text/babel" src="../src/components/WeatherCard.jsx"></script>
  <script type="text/babel" src="../src/components/WeatherTrends.jsx"></script>
  <script type="text/babel" src="../src/App.jsx"></script>
  <script type="text/babel" src="../src/index.js"></script>
</body>
</html>